{"name": "@bifrost_inc/superclaude", "version": "4.0.8", "description": "SuperClaude Framework NPM wrapper - Official Node.js wrapper for the Python SuperClaude package. Enhances Claude Code with specialized commands and AI development tools.", "scripts": {"postinstall": "node ./bin/install.js", "update": "node ./bin/update.js", "lint": "eslint . --ext .js,.mjs,.cjs", "test": "echo \"No tests defined yet\" && exit 0"}, "files": ["bin/", "README.md", "LICENSE"], "author": {"name": "SuperC<PERSON><PERSON>", "url": "https://github.com/SuperClaude-Org"}, "repository": {"type": "git", "url": "git+https://github.com/SuperClaude-Org/SuperClaude_Framework.git"}, "bugs": {"url": "https://github.com/SuperClaude-Org/SuperClaude_Framework/issues"}, "homepage": "https://github.com/SuperClaude-Org/SuperClaude_Framework#readme", "license": "MIT", "keywords": ["superclaude", "ai", "cli", "pypi", "python", "wrapper", "cross-platform", "automation"], "engines": {"node": ">=16"}, "funding": {"type": "github", "url": "https://github.com/sponsors/NomenAK"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "preferGlobal": true, "type": "commonjs"}