#!/usr/bin/env python3
"""
SuperClaude实体集成脚本
自动将8个SuperClaude实体类型添加到现有的Graphiti MCP服务器中

使用方法:
python integrate_entities.py /path/to/graphiti_mcp_server.py
"""

import sys
import re
import os
from pathlib import Path


def read_file(file_path):
    """读取文件内容"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read()


def write_file(file_path, content):
    """写入文件内容"""
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)


def backup_file(file_path):
    """备份原始文件"""
    backup_path = f"{file_path}.backup"
    content = read_file(file_path)
    write_file(backup_path, content)
    print(f"✅ 已备份原始文件到: {backup_path}")


def get_superclaude_entities():
    """获取SuperClaude实体定义"""
    return '''
# ============================================================================
# SUPERCLAUDE EXTENDED ENTITIES
# ============================================================================

class DevelopmentPreference(BaseModel):
    """用户的开发偏好和选择"""
    category: str = Field(..., description="framework|language|tool|pattern|architecture")
    preference: str = Field(..., description="具体偏好或选择")
    reasoning: str = Field(..., description="偏好存在的原因")
    confidence_score: float = Field(..., description="0.0-1.0置信度")
    context: str = Field(..., description="偏好适用的场景")
    last_updated: datetime = Field(default_factory=datetime.now)
    success_rate: float = Field(default=0.5, description="历史成功率")


class ProjectExperience(BaseModel):
    """详细的项目经验和成果"""
    project_name: str = Field(..., description="项目标识符")
    project_type: str = Field(..., description="web_app|mobile_app|api|library|tool")
    domain: str = Field(..., description="e-commerce|fintech|healthcare|education等")
    technologies: List[str] = Field(..., description="使用的技术栈")
    team_size: int = Field(default=1, description="团队成员数量")
    duration_weeks: int = Field(..., description="项目持续时间")
    challenges: List[str] = Field(default_factory=list, description="面临的主要挑战")
    solutions: List[str] = Field(default_factory=list, description="挑战的解决方案")
    lessons_learned: List[str] = Field(default_factory=list, description="关键收获")
    success_metrics: Dict[str, Any] = Field(default_factory=dict)
    satisfaction_score: float = Field(..., description="0.0-1.0项目满意度")
    would_repeat: bool = Field(default=True, description="是否会重复相同方法")


class DecisionHistory(BaseModel):
    """重要技术决策的记录"""
    decision_context: str = Field(..., description="决策背景")
    options_considered: List[str] = Field(..., description="考虑的所有选项")
    chosen_option: str = Field(..., description="最终选择")
    decision_criteria: List[str] = Field(default_factory=list, description="决策标准")
    reasoning: str = Field(..., description="详细推理")
    stakeholders: List[str] = Field(default_factory=list, description="参与决策的人")
    outcome: str = Field(default="", description="实际结果")
    outcome_rating: float = Field(default=0.5, description="0.0-1.0效果评分")
    lessons_learned: str = Field(default="", description="从结果中学到的")
    would_decide_differently: bool = Field(default=False, description="回顾性评估")
    timestamp: datetime = Field(default_factory=datetime.now)


class ToolUsagePattern(BaseModel):
    """工具使用模式和效果"""
    tool_name: str = Field(..., description="工具或技术名称")
    tool_category: str = Field(..., description="ide|framework|library|service|cli")
    use_cases: List[str] = Field(..., description="工具使用场景")
    effectiveness_rating: float = Field(..., description="0.0-1.0效果评分")
    learning_curve: str = Field(default="moderate", description="easy|moderate|steep")
    common_parameters: Dict[str, Any] = Field(default_factory=dict)
    best_practices: List[str] = Field(default_factory=list, description="最佳实践")
    common_pitfalls: List[str] = Field(default_factory=list, description="常见陷阱")
    alternatives_considered: List[str] = Field(default_factory=list)
    replacement_likelihood: float = Field(default=0.1, description="替换可能性")
    last_used: datetime = Field(default_factory=datetime.now)


class LearningProgress(BaseModel):
    """技能发展和知识获取跟踪"""
    skill_area: str = Field(..., description="学习的技能领域")
    current_level: str = Field(..., description="beginner|intermediate|advanced|expert")
    learning_goals: List[str] = Field(..., description="学习目标")
    resources_used: List[str] = Field(default_factory=list, description="使用的资源")
    practice_projects: List[str] = Field(default_factory=list, description="练习项目")
    milestones_achieved: List[str] = Field(default_factory=list, description="达成的里程碑")
    knowledge_gaps: List[str] = Field(default_factory=list, description="知识缺口")
    next_steps: List[str] = Field(default_factory=list, description="下一步计划")
    confidence_level: float = Field(..., description="0.0-1.0技能信心")
    last_practiced: datetime = Field(default_factory=datetime.now)
    started_learning: datetime = Field(default_factory=datetime.now)


class ProblemSolution(BaseModel):
    """问题和解决方案目录"""
    problem_description: str = Field(..., description="遇到的问题")
    problem_category: str = Field(..., description="bug|performance|design|deployment|security")
    context: str = Field(..., description="问题发生的场景")
    symptoms: List[str] = Field(..., description="问题表现")
    root_cause: str = Field(..., description="根本原因")
    solution_steps: List[str] = Field(..., description="解决步骤")
    tools_used: List[str] = Field(default_factory=list, description="使用的工具")
    time_to_solve: int = Field(..., description="解决时间（分钟）")
    solution_effectiveness: float = Field(..., description="0.0-1.0解决效果")
    prevention_measures: List[str] = Field(default_factory=list, description="预防措施")
    similar_problems: List[str] = Field(default_factory=list, description="相似问题")
    solved_date: datetime = Field(default_factory=datetime.now)


class CodePattern(BaseModel):
    """可重用的代码模式和架构"""
    pattern_name: str = Field(..., description="模式名称")
    pattern_type: str = Field(..., description="design|architectural|implementation")
    use_case: str = Field(..., description="使用场景")
    technologies: List[str] = Field(..., description="适用的语言/框架")
    code_example: str = Field(..., description="示例实现")
    benefits: List[str] = Field(..., description="使用优势")
    drawbacks: List[str] = Field(default_factory=list, description="潜在缺点")
    alternatives: List[str] = Field(default_factory=list, description="替代模式")
    complexity_level: str = Field(default="moderate", description="simple|moderate|complex")
    usage_frequency: float = Field(default=0.1, description="0.0-1.0使用频率")
    success_rate: float = Field(default=0.5, description="0.0-1.0成功率")
    created_date: datetime = Field(default_factory=datetime.now)


class WorkflowPreference(BaseModel):
    """首选工作流和流程"""
    workflow_name: str = Field(..., description="工作流名称")
    workflow_type: str = Field(..., description="development|testing|deployment|review")
    steps: List[str] = Field(..., description="有序的工作流步骤")
    tools_involved: List[str] = Field(..., description="涉及的工具")
    time_estimate: int = Field(..., description="典型时间（分钟）")
    success_criteria: List[str] = Field(..., description="成功标准")
    common_variations: List[str] = Field(default_factory=list, description="常见变体")
    automation_level: float = Field(default=0.0, description="0.0-1.0自动化程度")
    satisfaction_rating: float = Field(..., description="0.0-1.0满意度")
    improvement_ideas: List[str] = Field(default_factory=list, description="改进想法")
    last_used: datetime = Field(default_factory=datetime.now)
'''


def get_entity_mapping_addition():
    """获取实体映射添加内容"""
    return '''
    # SuperClaude扩展实体
    "DevelopmentPreference": DevelopmentPreference,
    "ProjectExperience": ProjectExperience,
    "DecisionHistory": DecisionHistory,
    "ToolUsagePattern": ToolUsagePattern,
    "LearningProgress": LearningProgress,
    "ProblemSolution": ProblemSolution,
    "CodePattern": CodePattern,
    "WorkflowPreference": WorkflowPreference,'''


def ensure_imports(content):
    """确保必要的导入存在"""
    imports_to_add = [
        "from datetime import datetime",
        "from typing import List, Dict, Any, Optional"
    ]
    
    for import_line in imports_to_add:
        if import_line not in content:
            # 在文件开头添加导入
            lines = content.split('\n')
            # 找到第一个非注释、非空行
            insert_index = 0
            for i, line in enumerate(lines):
                if line.strip() and not line.strip().startswith('#') and not line.strip().startswith('"""'):
                    insert_index = i
                    break
            
            lines.insert(insert_index, import_line)
            content = '\n'.join(lines)
    
    return content


def integrate_entities(file_path):
    """集成SuperClaude实体到Graphiti MCP服务器"""
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    print(f"🔄 开始集成SuperClaude实体到: {file_path}")
    
    # 备份原始文件
    backup_file(file_path)
    
    # 读取原始内容
    content = read_file(file_path)
    
    # 确保必要的导入
    content = ensure_imports(content)
    
    # 查找Requirement类定义的位置
    requirement_match = re.search(r'class Requirement\(BaseModel\):', content)
    if not requirement_match:
        print("❌ 未找到Requirement类定义，请确认这是正确的Graphiti MCP服务器文件")
        return False
    
    # 在Requirement类后添加SuperClaude实体
    requirement_end = content.find('\n\n', requirement_match.end())
    if requirement_end == -1:
        requirement_end = len(content)
    
    superclaude_entities = get_superclaude_entities()
    content = content[:requirement_end] + superclaude_entities + content[requirement_end:]
    
    # 查找实体类型映射
    entity_types_pattern = r'ENTITY_TYPES\s*=\s*\{[^}]*\}'
    entity_types_match = re.search(entity_types_pattern, content, re.DOTALL)
    
    if entity_types_match:
        # 在现有映射中添加新实体
        original_mapping = entity_types_match.group(0)
        # 移除最后的 }
        mapping_without_end = original_mapping.rstrip().rstrip('}')
        # 添加新实体
        new_mapping = mapping_without_end + get_entity_mapping_addition() + '\n}'
        content = content.replace(original_mapping, new_mapping)
        print("✅ 已更新实体类型映射")
    else:
        print("⚠️  未找到ENTITY_TYPES映射，请手动添加实体映射")
    
    # 写入更新后的内容
    write_file(file_path, content)
    
    print("✅ SuperClaude实体集成完成！")
    print("\n📋 集成的实体类型:")
    entities = [
        "DevelopmentPreference - 开发偏好",
        "ProjectExperience - 项目经验", 
        "DecisionHistory - 决策历史",
        "ToolUsagePattern - 工具使用模式",
        "LearningProgress - 学习进度",
        "ProblemSolution - 问题解决方案",
        "CodePattern - 代码模式",
        "WorkflowPreference - 工作流偏好"
    ]
    
    for entity in entities:
        print(f"  ✓ {entity}")
    
    print(f"\n🔄 请重启Graphiti MCP服务器以应用更改")
    print(f"📁 原始文件已备份到: {file_path}.backup")
    
    return True


def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法: python integrate_entities.py <graphiti_mcp_server.py的路径>")
        print("示例: python integrate_entities.py ~/graphiti/mcp_server/graphiti_mcp_server.py")
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    print("🚀 SuperClaude实体集成工具")
    print("=" * 50)
    
    success = integrate_entities(file_path)
    
    if success:
        print("\n🎉 集成成功完成！")
        print("\n📝 下一步:")
        print("1. 重启Graphiti MCP服务器")
        print("2. 在SuperClaude中测试新实体")
        print("3. 开始记录您的开发偏好和经验")
    else:
        print("\n❌ 集成失败，请检查错误信息")
        sys.exit(1)


if __name__ == "__main__":
    main()
