# Graphiti MCP Server 使用手册

## 概述

Graphiti MCP Server 是一个基于知识图谱的AI代理记忆工具，通过Model Context Protocol (MCP)协议为AI助手提供强大的知识图谱记忆能力。它能够持续整合用户交互、结构化和非结构化数据，构建可查询的动态知识图谱。

## 核心特性

### 🧠 智能记忆管理
- **时间感知**：支持双时间模型，跟踪事件发生时间和数据摄入时间
- **增量更新**：实时数据集成，无需批量重计算
- **动态演化**：知识图谱随新信息自动更新和演化

### 🔍 多维搜索能力
- **语义搜索**：基于向量嵌入的语义相似性搜索
- **关键词搜索**：BM25算法支持的精确关键词匹配
- **图遍历搜索**：基于图结构的关系探索
- **混合搜索**：结合多种搜索方式的最优结果

### 📊 灵活数据支持
- **文本数据**：自然语言文本处理
- **JSON数据**：结构化数据自动实体关系提取
- **消息数据**：对话和交互记录
- **自定义实体**：支持用户定义的实体类型

### 🔧 企业级特性
- **高并发处理**：可配置的并发限制
- **多租户支持**：通过group_id实现数据隔离
- **可扩展性**：支持大规模数据集
- **容错机制**：队列化处理确保数据一致性

## 系统要求

### 必需组件
- **Python**: 3.10或更高版本
- **数据库**: Neo4j 5.26+
- **API密钥**: OpenAI API密钥（用于LLM操作）

### 可选组件
- **Docker**: 用于容器化部署
- **Azure OpenAI**: 替代OpenAI的企业级选择
- **其他LLM提供商**: Anthropic、Groq、Google Gemini等

## 安装配置

### 方式一：Docker部署（推荐）

1. **克隆项目**
```bash
git clone https://github.com/getzep/graphiti.git
cd graphiti/mcp_server
```

2. **配置环境变量**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，设置必要参数
OPENAI_API_KEY=your_openai_api_key_here
MODEL_NAME=gpt-4.1-mini
NEO4J_PASSWORD=demodemo
```

3. **启动服务**
```bash
docker compose up
```

### 方式二：本地安装

1. **安装依赖**
```bash
# 安装uv包管理器
curl -LsSf https://astral.sh/uv/install.sh | sh

# 安装项目依赖
uv sync
```

2. **配置数据库**
```bash
# 启动Neo4j（使用Neo4j Desktop或Docker）
docker run -p 7474:7474 -p 7687:7687 \
  -e NEO4J_AUTH=neo4j/demodemo \
  neo4j:5.26.0
```

3. **运行服务器**
```bash
uv run graphiti_mcp_server.py --transport sse
```

## 环境变量配置

### 基础配置
```bash
# Neo4j数据库连接
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=demodemo

# OpenAI配置
OPENAI_API_KEY=sk-your-api-key
MODEL_NAME=gpt-4.1-mini
SMALL_MODEL_NAME=gpt-4.1-nano
LLM_TEMPERATURE=0.0

# 并发控制
SEMAPHORE_LIMIT=10
```

### Azure OpenAI配置
```bash
# Azure OpenAI LLM配置
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_DEPLOYMENT_NAME=your-deployment-name
AZURE_OPENAI_API_VERSION=2024-02-15-preview

# Azure OpenAI嵌入配置
AZURE_OPENAI_EMBEDDING_ENDPOINT=https://your-embedding-resource.openai.azure.com/
AZURE_OPENAI_EMBEDDING_DEPLOYMENT_NAME=your-embedding-deployment
AZURE_OPENAI_EMBEDDING_API_VERSION=2024-02-15-preview

# 使用托管身份认证
AZURE_OPENAI_USE_MANAGED_IDENTITY=true
```

## 命令行参数

```bash
uv run graphiti_mcp_server.py [选项]

选项:
  --group-id TEXT          图谱命名空间ID
  --transport {sse,stdio}  传输协议 (默认: sse)
  --model TEXT            LLM模型名称
  --small-model TEXT      小型LLM模型名称
  --temperature FLOAT     LLM温度参数 (0.0-2.0)
  --destroy-graph         启动时清空图谱
  --use-custom-entities   启用自定义实体提取
  --host TEXT             服务器绑定地址
```

## 核心功能详解

### 1. 记忆管理 (add_memory)

向知识图谱添加记忆片段，支持多种数据格式。

**参数说明：**
- `name`: 记忆片段名称
- `episode_body`: 记忆内容
- `group_id`: 可选，数据分组ID
- `source`: 数据类型 (text/json/message)
- `source_description`: 可选，数据源描述
- `uuid`: 可选，自定义UUID

**使用示例：**

```python
# 添加文本记忆
add_memory(
    name="公司新闻",
    episode_body="Acme公司今天宣布推出新产品线。",
    source="text",
    source_description="新闻文章",
    group_id="company_news"
)

# 添加JSON结构化数据
add_memory(
    name="客户档案",
    episode_body='{"company": {"name": "Acme Technologies"}, "products": [{"id": "P001", "name": "CloudSync"}]}',
    source="json",
    source_description="CRM数据"
)

# 添加对话记录
add_memory(
    name="客户对话",
    episode_body="用户: 你们的退货政策是什么？\n助手: 您可以在30天内退货。",
    source="message",
    source_description="聊天记录"
)
```

### 2. 节点搜索 (search_memory_nodes)

搜索知识图谱中的实体节点摘要。

**参数说明：**
- `query`: 搜索查询
- `group_ids`: 可选，限制搜索的分组
- `max_nodes`: 最大返回节点数 (默认: 10)
- `center_node_uuid`: 可选，中心节点UUID
- `entity`: 可选，实体类型过滤

**使用示例：**

```python
# 基础节点搜索
search_memory_nodes(
    query="Acme公司的产品",
    max_nodes=5
)

# 按实体类型过滤
search_memory_nodes(
    query="用户偏好",
    entity="Preference",
    group_ids=["user_data"]
)

# 围绕特定节点搜索
search_memory_nodes(
    query="相关产品",
    center_node_uuid="node-uuid-here",
    max_nodes=10
)
```

### 3. 事实搜索 (search_memory_facts)

搜索实体间的关系和事实。

**参数说明：**
- `query`: 搜索查询
- `group_ids`: 可选，限制搜索的分组
- `max_facts`: 最大返回事实数 (默认: 10)
- `center_node_uuid`: 可选，中心节点UUID

**使用示例：**

```python
# 搜索相关事实
search_memory_facts(
    query="Acme公司和产品的关系",
    max_facts=5
)

# 围绕特定节点搜索事实
search_memory_facts(
    query="产品特性",
    center_node_uuid="product-node-uuid",
    max_facts=10
)
```

### 4. 记忆检索 (get_episodes)

获取最近的记忆片段。

**参数说明：**
- `group_id`: 可选，分组ID
- `last_n`: 返回最近N个片段 (默认: 10)

**使用示例：**

```python
# 获取最近的记忆
get_episodes(last_n=5)

# 获取特定分组的记忆
get_episodes(
    group_id="customer_interactions",
    last_n=10
)
```

### 5. 数据管理

**删除操作：**
```python
# 删除实体关系
delete_entity_edge(uuid="edge-uuid")

# 删除记忆片段
delete_episode(uuid="episode-uuid")

# 清空整个图谱
clear_graph()
```

**获取特定数据：**
```python
# 获取特定实体关系
get_entity_edge(uuid="edge-uuid")
```

## MCP客户端集成

### Claude Desktop集成

1. **配置文件位置**
   - macOS: `~/Library/Application Support/Claude/claude_desktop_config.json`
   - Windows: `%APPDATA%\Claude\claude_desktop_config.json`

2. **STDIO传输配置**
```json
{
  "mcpServers": {
    "graphiti-memory": {
      "transport": "stdio",
      "command": "/path/to/uv",
      "args": [
        "run",
        "--directory",
        "/path/to/graphiti/mcp_server",
        "graphiti_mcp_server.py",
        "--transport",
        "stdio"
      ],
      "env": {
        "NEO4J_URI": "bolt://localhost:7687",
        "NEO4J_USER": "neo4j",
        "NEO4J_PASSWORD": "demodemo",
        "OPENAI_API_KEY": "your-api-key",
        "MODEL_NAME": "gpt-4.1-mini"
      }
    }
  }
}
```

3. **SSE传输配置（需要mcp-remote）**
```json
{
  "mcpServers": {
    "graphiti-memory": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "http://localhost:8000/sse"
      ]
    }
  }
}
```

### Cursor IDE集成

1. **启动SSE服务器**
```bash
docker compose up
# 或
uv run graphiti_mcp_server.py --transport sse --group-id my_project
```

2. **Cursor配置**
```json
{
  "mcpServers": {
    "graphiti-memory": {
      "url": "http://localhost:8000/sse"
    }
  }
}
```

3. **添加Cursor规则**
将`cursor_rules.md`中的规则添加到Cursor的用户规则中，以优化AI助手的记忆使用。

## 自定义实体类型

Graphiti支持三种预定义实体类型：

### 1. Requirement（需求）
用于捕获项目需求和功能规格。

```python
# 启用自定义实体
uv run graphiti_mcp_server.py --use-custom-entities

# 添加包含需求的记忆
add_memory(
    name="项目需求",
    episode_body="系统必须支持用户认证和权限管理。",
    source="text"
)
```

### 2. Preference（偏好）
记录用户的喜好和偏好设置。

```python
add_memory(
    name="用户偏好",
    episode_body="我喜欢简洁的界面设计，不喜欢过于复杂的菜单。",
    source="text"
)
```

### 3. Procedure（流程）
保存操作步骤和工作流程。

```python
add_memory(
    name="部署流程",
    episode_body="首先运行测试，然后构建镜像，最后部署到生产环境。",
    source="text"
)
```

## 最佳实践

### 1. 数据组织
- **使用group_id**：为不同项目或用户创建独立的数据空间
- **描述性命名**：为记忆片段使用清晰、描述性的名称
- **结构化数据**：充分利用JSON格式的自动实体提取能力

### 2. 搜索优化
- **具体查询**：使用具体、明确的搜索词汇
- **组合搜索**：结合节点搜索和事实搜索获得完整信息
- **中心节点**：利用center_node_uuid探索相关信息

### 3. 性能调优
- **并发控制**：根据LLM提供商的速率限制调整SEMAPHORE_LIMIT
- **批量操作**：对于大量数据，考虑分批处理
- **定期维护**：定期清理不需要的数据

### 4. 安全考虑
- **API密钥管理**：使用环境变量或密钥管理服务
- **数据隔离**：通过group_id实现多租户数据隔离
- **访问控制**：在生产环境中配置适当的网络访问控制

## 故障排除

### 常见问题

1. **连接失败**
```bash
# 检查Neo4j状态
docker ps | grep neo4j

# 验证连接
curl http://localhost:7474
```

2. **API密钥错误**
```bash
# 验证环境变量
echo $OPENAI_API_KEY

# 检查日志
docker compose logs graphiti-mcp
```

3. **内存不足**
```bash
# 调整Neo4j内存设置
NEO4J_server_memory_heap_max__size=2G
```

4. **速率限制**
```bash
# 降低并发数
SEMAPHORE_LIMIT=5
```

### 日志分析

服务器日志包含详细的操作信息：
```bash
# 查看实时日志
docker compose logs -f graphiti-mcp

# 查看特定时间段日志
docker compose logs --since="2024-01-01T00:00:00" graphiti-mcp
```

### 性能监控

监控关键指标：
- 记忆添加速度
- 搜索响应时间
- 数据库连接状态
- 内存使用情况

## 高级配置

### 多模型配置

```python
# 配置不同用途的模型
MODEL_NAME=gpt-4.1-mini          # 主要LLM
SMALL_MODEL_NAME=gpt-4.1-nano    # 轻量级操作
EMBEDDER_MODEL_NAME=text-embedding-3-small  # 嵌入模型
```

### 自定义驱动器

```python
# 使用自定义数据库驱动
from graphiti_core.driver.neo4j_driver import Neo4jDriver

driver = Neo4jDriver(
    uri="bolt://localhost:7687",
    user="neo4j",
    password="password",
    database="custom_db"
)
```

### 遥测配置

```bash
# 禁用遥测
GRAPHITI_TELEMETRY_ENABLED=false
```

## 总结

Graphiti MCP Server为AI代理提供了强大的知识图谱记忆能力，支持多种数据格式、灵活的搜索方式和企业级的可扩展性。通过合理的配置和使用，可以显著提升AI助手的上下文理解和个性化服务能力。

关键优势：
- ✅ 实时动态更新
- ✅ 多维度搜索
- ✅ 时间感知能力
- ✅ 企业级可扩展性
- ✅ 易于集成

建议在实际使用中根据具体需求调整配置参数，并定期监控系统性能以确保最佳体验。
