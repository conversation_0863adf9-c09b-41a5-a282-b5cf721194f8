{"graphiti": {"command": "uv", "args": ["run", "--isolated", "--directory", "~/graphiti/mcp_server", "--project", ".", "graphiti_mcp_server.py", "--transport", "stdio"], "env": {"NEO4J_URI": "bolt://localhost:7687", "NEO4J_USER": "neo4j", "NEO4J_PASSWORD": "graphiti123!", "OPENAI_API_KEY": "${OPENAI_API_KEY}", "MODEL_NAME": "gpt-4o-mini", "GRAPHITI_GROUP_ID": "superclaude_user", "GRAPHITI_LOG_LEVEL": "INFO"}}}