#!/usr/bin/env python3
"""
Extended Graphiti MCP Server for SuperClaude
Includes all 8 SuperClaude entity types plus original Requirement entity

This is an example of how to modify the original graphiti_mcp_server.py
to include the SuperClaude entity extensions.
"""

import asyncio
import json
import logging
import os
from datetime import datetime
from typing import Any, Dict, List, Optional, Sequence

from graphiti import Graphiti
from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
)
from pydantic import BaseModel, Field

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Graphiti client
graphiti_client = Graphiti()

# Initialize MCP server
server = Server("graphiti-memory")


# ============================================================================
# ENTITY DEFINITIONS
# ============================================================================

class Requirement(BaseModel):
    """需求实体 - 代表产品或服务必须满足的特定需要、功能或功能"""
    project_name: str = Field(..., description='需求所属项目的名称')
    description: str = Field(..., description='需求的描述，仅使用上下文中提到的信息')


class DevelopmentPreference(BaseModel):
    """用户的开发偏好和选择"""
    category: str = Field(..., description="framework|language|tool|pattern|architecture")
    preference: str = Field(..., description="具体偏好或选择")
    reasoning: str = Field(..., description="偏好存在的原因")
    confidence_score: float = Field(..., description="0.0-1.0置信度")
    context: str = Field(..., description="偏好适用的场景")
    last_updated: datetime = Field(default_factory=datetime.now)
    success_rate: float = Field(default=0.5, description="历史成功率")


class ProjectExperience(BaseModel):
    """详细的项目经验和成果"""
    project_name: str = Field(..., description="项目标识符")
    project_type: str = Field(..., description="web_app|mobile_app|api|library|tool")
    domain: str = Field(..., description="e-commerce|fintech|healthcare|education等")
    technologies: List[str] = Field(..., description="使用的技术栈")
    team_size: int = Field(default=1, description="团队成员数量")
    duration_weeks: int = Field(..., description="项目持续时间")
    challenges: List[str] = Field(default_factory=list, description="面临的主要挑战")
    solutions: List[str] = Field(default_factory=list, description="挑战的解决方案")
    lessons_learned: List[str] = Field(default_factory=list, description="关键收获")
    success_metrics: Dict[str, Any] = Field(default_factory=dict)
    satisfaction_score: float = Field(..., description="0.0-1.0项目满意度")
    would_repeat: bool = Field(default=True, description="是否会重复相同方法")


class DecisionHistory(BaseModel):
    """重要技术决策的记录"""
    decision_context: str = Field(..., description="决策背景")
    options_considered: List[str] = Field(..., description="考虑的所有选项")
    chosen_option: str = Field(..., description="最终选择")
    decision_criteria: List[str] = Field(default_factory=list, description="决策标准")
    reasoning: str = Field(..., description="详细推理")
    stakeholders: List[str] = Field(default_factory=list, description="参与决策的人")
    outcome: str = Field(default="", description="实际结果")
    outcome_rating: float = Field(default=0.5, description="0.0-1.0效果评分")
    lessons_learned: str = Field(default="", description="从结果中学到的")
    would_decide_differently: bool = Field(default=False, description="回顾性评估")
    timestamp: datetime = Field(default_factory=datetime.now)


class ToolUsagePattern(BaseModel):
    """工具使用模式和效果"""
    tool_name: str = Field(..., description="工具或技术名称")
    tool_category: str = Field(..., description="ide|framework|library|service|cli")
    use_cases: List[str] = Field(..., description="工具使用场景")
    effectiveness_rating: float = Field(..., description="0.0-1.0效果评分")
    learning_curve: str = Field(default="moderate", description="easy|moderate|steep")
    common_parameters: Dict[str, Any] = Field(default_factory=dict)
    best_practices: List[str] = Field(default_factory=list, description="最佳实践")
    common_pitfalls: List[str] = Field(default_factory=list, description="常见陷阱")
    alternatives_considered: List[str] = Field(default_factory=list)
    replacement_likelihood: float = Field(default=0.1, description="替换可能性")
    last_used: datetime = Field(default_factory=datetime.now)


class LearningProgress(BaseModel):
    """技能发展和知识获取跟踪"""
    skill_area: str = Field(..., description="学习的技能领域")
    current_level: str = Field(..., description="beginner|intermediate|advanced|expert")
    learning_goals: List[str] = Field(..., description="学习目标")
    resources_used: List[str] = Field(default_factory=list, description="使用的资源")
    practice_projects: List[str] = Field(default_factory=list, description="练习项目")
    milestones_achieved: List[str] = Field(default_factory=list, description="达成的里程碑")
    knowledge_gaps: List[str] = Field(default_factory=list, description="知识缺口")
    next_steps: List[str] = Field(default_factory=list, description="下一步计划")
    confidence_level: float = Field(..., description="0.0-1.0技能信心")
    last_practiced: datetime = Field(default_factory=datetime.now)
    started_learning: datetime = Field(default_factory=datetime.now)


class ProblemSolution(BaseModel):
    """问题和解决方案目录"""
    problem_description: str = Field(..., description="遇到的问题")
    problem_category: str = Field(..., description="bug|performance|design|deployment|security")
    context: str = Field(..., description="问题发生的场景")
    symptoms: List[str] = Field(..., description="问题表现")
    root_cause: str = Field(..., description="根本原因")
    solution_steps: List[str] = Field(..., description="解决步骤")
    tools_used: List[str] = Field(default_factory=list, description="使用的工具")
    time_to_solve: int = Field(..., description="解决时间（分钟）")
    solution_effectiveness: float = Field(..., description="0.0-1.0解决效果")
    prevention_measures: List[str] = Field(default_factory=list, description="预防措施")
    similar_problems: List[str] = Field(default_factory=list, description="相似问题")
    solved_date: datetime = Field(default_factory=datetime.now)


class CodePattern(BaseModel):
    """可重用的代码模式和架构"""
    pattern_name: str = Field(..., description="模式名称")
    pattern_type: str = Field(..., description="design|architectural|implementation")
    use_case: str = Field(..., description="使用场景")
    technologies: List[str] = Field(..., description="适用的语言/框架")
    code_example: str = Field(..., description="示例实现")
    benefits: List[str] = Field(..., description="使用优势")
    drawbacks: List[str] = Field(default_factory=list, description="潜在缺点")
    alternatives: List[str] = Field(default_factory=list, description="替代模式")
    complexity_level: str = Field(default="moderate", description="simple|moderate|complex")
    usage_frequency: float = Field(default=0.1, description="0.0-1.0使用频率")
    success_rate: float = Field(default=0.5, description="0.0-1.0成功率")
    created_date: datetime = Field(default_factory=datetime.now)


class WorkflowPreference(BaseModel):
    """首选工作流和流程"""
    workflow_name: str = Field(..., description="工作流名称")
    workflow_type: str = Field(..., description="development|testing|deployment|review")
    steps: List[str] = Field(..., description="有序的工作流步骤")
    tools_involved: List[str] = Field(..., description="涉及的工具")
    time_estimate: int = Field(..., description="典型时间（分钟）")
    success_criteria: List[str] = Field(..., description="成功标准")
    common_variations: List[str] = Field(default_factory=list, description="常见变体")
    automation_level: float = Field(default=0.0, description="0.0-1.0自动化程度")
    satisfaction_rating: float = Field(..., description="0.0-1.0满意度")
    improvement_ideas: List[str] = Field(default_factory=list, description="改进想法")
    last_used: datetime = Field(default_factory=datetime.now)


# Entity type mapping
ENTITY_TYPES = {
    "Requirement": Requirement,
    "DevelopmentPreference": DevelopmentPreference,
    "ProjectExperience": ProjectExperience,
    "DecisionHistory": DecisionHistory,
    "ToolUsagePattern": ToolUsagePattern,
    "LearningProgress": LearningProgress,
    "ProblemSolution": ProblemSolution,
    "CodePattern": CodePattern,
    "WorkflowPreference": WorkflowPreference,
}


# ============================================================================
# MCP SERVER TOOLS
# ============================================================================

@server.list_tools()
async def handle_list_tools() -> List[Tool]:
    """List available tools for memory management"""
    return [
        Tool(
            name="add_episode",
            description="Add a new episode (interaction) to memory with entity extraction",
            inputSchema={
                "type": "object",
                "properties": {
                    "content": {
                        "type": "string",
                        "description": "The content to add to memory"
                    },
                    "entity_types": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Specific entity types to extract",
                        "default": list(ENTITY_TYPES.keys())
                    }
                },
                "required": ["content"]
            }
        ),
        Tool(
            name="search_memories",
            description="Search through stored memories and entities",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Search query"
                    },
                    "entity_types": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Filter by entity types"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Maximum number of results",
                        "default": 10
                    }
                },
                "required": ["query"]
            }
        ),
        Tool(
            name="get_entity_relationships",
            description="Get relationships for a specific entity",
            inputSchema={
                "type": "object",
                "properties": {
                    "entity_id": {
                        "type": "string",
                        "description": "Entity UUID"
                    }
                },
                "required": ["entity_id"]
            }
        ),
        Tool(
            name="update_entity",
            description="Update an existing entity",
            inputSchema={
                "type": "object",
                "properties": {
                    "entity_id": {
                        "type": "string",
                        "description": "Entity UUID to update"
                    },
                    "updates": {
                        "type": "object",
                        "description": "Fields to update"
                    }
                },
                "required": ["entity_id", "updates"]
            }
        )
    ]


@server.call_tool()
async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle tool calls for memory operations"""
    
    if name == "add_episode":
        content = arguments["content"]
        entity_types = arguments.get("entity_types", list(ENTITY_TYPES.keys()))
        
        try:
            # Add episode to Graphiti
            episode_id = await graphiti_client.add_episode(
                content=content,
                entity_types=entity_types
            )
            
            return [TextContent(
                type="text",
                text=f"Successfully added episode to memory. Episode ID: {episode_id}"
            )]
            
        except Exception as e:
            logger.error(f"Error adding episode: {e}")
            return [TextContent(
                type="text",
                text=f"Error adding episode: {str(e)}"
            )]
    
    elif name == "search_memories":
        query = arguments["query"]
        entity_types = arguments.get("entity_types")
        limit = arguments.get("limit", 10)
        
        try:
            # Search memories in Graphiti
            results = await graphiti_client.search(
                query=query,
                entity_types=entity_types,
                limit=limit
            )
            
            formatted_results = []
            for result in results:
                formatted_results.append(f"- {result.get('content', 'No content')}")
            
            return [TextContent(
                type="text",
                text=f"Found {len(results)} memories:\n" + "\n".join(formatted_results)
            )]
            
        except Exception as e:
            logger.error(f"Error searching memories: {e}")
            return [TextContent(
                type="text",
                text=f"Error searching memories: {str(e)}"
            )]
    
    elif name == "get_entity_relationships":
        entity_id = arguments["entity_id"]
        
        try:
            # Get entity relationships from Graphiti
            relationships = await graphiti_client.get_entity_relationships(entity_id)
            
            formatted_relationships = []
            for rel in relationships:
                formatted_relationships.append(
                    f"- {rel.get('type', 'Unknown')} -> {rel.get('target', 'Unknown')}"
                )
            
            return [TextContent(
                type="text",
                text=f"Entity relationships:\n" + "\n".join(formatted_relationships)
            )]
            
        except Exception as e:
            logger.error(f"Error getting relationships: {e}")
            return [TextContent(
                type="text",
                text=f"Error getting relationships: {str(e)}"
            )]
    
    elif name == "update_entity":
        entity_id = arguments["entity_id"]
        updates = arguments["updates"]
        
        try:
            # Update entity in Graphiti
            await graphiti_client.update_entity(entity_id, updates)
            
            return [TextContent(
                type="text",
                text=f"Successfully updated entity {entity_id}"
            )]
            
        except Exception as e:
            logger.error(f"Error updating entity: {e}")
            return [TextContent(
                type="text",
                text=f"Error updating entity: {str(e)}"
            )]
    
    else:
        return [TextContent(
            type="text",
            text=f"Unknown tool: {name}"
        )]


# ============================================================================
# SERVER INITIALIZATION
# ============================================================================

async def main():
    """Main server function"""
    # Initialize Graphiti with entity types
    await graphiti_client.build_indices_for_entity_types(list(ENTITY_TYPES.values()))
    
    # Run the server
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="graphiti-memory-extended",
                server_version="1.0.0",
                capabilities=server.get_capabilities(
                    notification_options=None,
                    experimental_capabilities=None,
                ),
            ),
        )


if __name__ == "__main__":
    asyncio.run(main())
