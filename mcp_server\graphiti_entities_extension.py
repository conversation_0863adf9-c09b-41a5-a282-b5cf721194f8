#!/usr/bin/env python3
"""
Extended Graphiti MCP Server with SuperClaude Entity Definitions
Adds 8 new entity types for comprehensive memory management
"""

from datetime import datetime
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field


class DevelopmentPreference(BaseModel):
    """User's development preferences and choices"""
    category: str = Field(
        ..., 
        description="framework|language|tool|pattern|architecture"
    )
    preference: str = Field(
        ..., 
        description="Specific preference or choice"
    )
    reasoning: str = Field(
        ..., 
        description="Why this preference exists"
    )
    confidence_score: float = Field(
        ..., 
        description="0.0-1.0 confidence level"
    )
    context: str = Field(
        ..., 
        description="When this preference applies"
    )
    last_updated: datetime = Field(
        default_factory=datetime.now,
        description="When preference was last confirmed"
    )
    success_rate: float = Field(
        default=0.5,
        description="Historical success rate with this preference"
    )


class ProjectExperience(BaseModel):
    """Detailed project experience and outcomes"""
    project_name: str = Field(
        ..., 
        description="Project identifier"
    )
    project_type: str = Field(
        ..., 
        description="web_app|mobile_app|api|library|tool"
    )
    domain: str = Field(
        ..., 
        description="e-commerce|fintech|healthcare|education|etc"
    )
    technologies: List[str] = Field(
        ..., 
        description="Tech stack used"
    )
    team_size: int = Field(
        default=1,
        description="Number of team members"
    )
    duration_weeks: int = Field(
        ..., 
        description="Project duration"
    )
    challenges: List[str] = Field(
        default_factory=list,
        description="Major challenges faced"
    )
    solutions: List[str] = Field(
        default_factory=list,
        description="How challenges were solved"
    )
    lessons_learned: List[str] = Field(
        default_factory=list,
        description="Key takeaways"
    )
    success_metrics: Dict[str, Any] = Field(
        default_factory=dict,
        description="Performance indicators"
    )
    satisfaction_score: float = Field(
        ..., 
        description="0.0-1.0 project satisfaction"
    )
    would_repeat: bool = Field(
        default=True,
        description="Would use same approach again"
    )


class DecisionHistory(BaseModel):
    """Record of significant technical decisions"""
    decision_context: str = Field(
        ..., 
        description="What decision was being made"
    )
    options_considered: List[str] = Field(
        ..., 
        description="All options evaluated"
    )
    chosen_option: str = Field(
        ..., 
        description="Final choice made"
    )
    decision_criteria: List[str] = Field(
        default_factory=list,
        description="Factors that influenced decision"
    )
    reasoning: str = Field(
        ..., 
        description="Detailed reasoning for choice"
    )
    stakeholders: List[str] = Field(
        default_factory=list,
        description="Who was involved in decision"
    )
    outcome: str = Field(
        default="",
        description="What actually happened"
    )
    outcome_rating: float = Field(
        default=0.5,
        description="0.0-1.0 how well it worked"
    )
    lessons_learned: str = Field(
        default="",
        description="What was learned from outcome"
    )
    would_decide_differently: bool = Field(
        default=False,
        description="Retrospective assessment"
    )
    timestamp: datetime = Field(
        default_factory=datetime.now,
        description="When decision was made"
    )


class ToolUsagePattern(BaseModel):
    """Patterns of tool usage and effectiveness"""
    tool_name: str = Field(
        ..., 
        description="Name of tool or technology"
    )
    tool_category: str = Field(
        ..., 
        description="ide|framework|library|service|cli"
    )
    use_cases: List[str] = Field(
        ..., 
        description="When this tool is used"
    )
    effectiveness_rating: float = Field(
        ..., 
        description="0.0-1.0 effectiveness score"
    )
    learning_curve: str = Field(
        default="moderate",
        description="easy|moderate|steep"
    )
    common_parameters: Dict[str, Any] = Field(
        default_factory=dict,
        description="Frequently used settings"
    )
    best_practices: List[str] = Field(
        default_factory=list,
        description="Learned best practices"
    )
    common_pitfalls: List[str] = Field(
        default_factory=list,
        description="Things to avoid"
    )
    alternatives_considered: List[str] = Field(
        default_factory=list,
        description="Other tools evaluated"
    )
    replacement_likelihood: float = Field(
        default=0.1,
        description="0.0-1.0 chance of switching"
    )
    last_used: datetime = Field(
        default_factory=datetime.now,
        description="When tool was last used"
    )


class LearningProgress(BaseModel):
    """Track skill development and knowledge acquisition"""
    skill_area: str = Field(
        ..., 
        description="What is being learned"
    )
    current_level: str = Field(
        ..., 
        description="beginner|intermediate|advanced|expert"
    )
    learning_goals: List[str] = Field(
        ..., 
        description="What user wants to achieve"
    )
    resources_used: List[str] = Field(
        default_factory=list,
        description="Books, courses, tutorials"
    )
    practice_projects: List[str] = Field(
        default_factory=list,
        description="Projects for skill building"
    )
    milestones_achieved: List[str] = Field(
        default_factory=list,
        description="Progress markers reached"
    )
    knowledge_gaps: List[str] = Field(
        default_factory=list,
        description="Areas needing improvement"
    )
    next_steps: List[str] = Field(
        default_factory=list,
        description="Planned learning activities"
    )
    confidence_level: float = Field(
        ..., 
        description="0.0-1.0 confidence in skill"
    )
    last_practiced: datetime = Field(
        default_factory=datetime.now,
        description="When skill was last used"
    )
    started_learning: datetime = Field(
        default_factory=datetime.now,
        description="When learning began"
    )


class ProblemSolution(BaseModel):
    """Catalog of problems and their solutions"""
    problem_description: str = Field(
        ..., 
        description="What problem was encountered"
    )
    problem_category: str = Field(
        ..., 
        description="bug|performance|design|deployment|security"
    )
    context: str = Field(
        ..., 
        description="When/where problem occurred"
    )
    symptoms: List[str] = Field(
        ..., 
        description="How problem manifested"
    )
    root_cause: str = Field(
        ..., 
        description="Underlying cause of problem"
    )
    solution_steps: List[str] = Field(
        ..., 
        description="How problem was solved"
    )
    tools_used: List[str] = Field(
        default_factory=list,
        description="Tools that helped solve it"
    )
    time_to_solve: int = Field(
        ..., 
        description="Minutes to resolve"
    )
    solution_effectiveness: float = Field(
        ..., 
        description="0.0-1.0 how well it worked"
    )
    prevention_measures: List[str] = Field(
        default_factory=list,
        description="How to avoid in future"
    )
    similar_problems: List[str] = Field(
        default_factory=list,
        description="Related issues"
    )
    solved_date: datetime = Field(
        default_factory=datetime.now,
        description="When problem was solved"
    )


class CodePattern(BaseModel):
    """Reusable code patterns and architectures"""
    pattern_name: str = Field(
        ..., 
        description="Name of the pattern"
    )
    pattern_type: str = Field(
        ..., 
        description="design|architectural|implementation"
    )
    use_case: str = Field(
        ..., 
        description="When to use this pattern"
    )
    technologies: List[str] = Field(
        ..., 
        description="Languages/frameworks it applies to"
    )
    code_example: str = Field(
        ..., 
        description="Sample implementation"
    )
    benefits: List[str] = Field(
        ..., 
        description="Advantages of using pattern"
    )
    drawbacks: List[str] = Field(
        default_factory=list,
        description="Potential disadvantages"
    )
    alternatives: List[str] = Field(
        default_factory=list,
        description="Other patterns to consider"
    )
    complexity_level: str = Field(
        default="moderate",
        description="simple|moderate|complex"
    )
    usage_frequency: float = Field(
        default=0.1,
        description="0.0-1.0 how often used"
    )
    success_rate: float = Field(
        default=0.5,
        description="0.0-1.0 success when applied"
    )
    created_date: datetime = Field(
        default_factory=datetime.now,
        description="When pattern was first recorded"
    )


class WorkflowPreference(BaseModel):
    """Preferred workflows and processes"""
    workflow_name: str = Field(
        ..., 
        description="Name of workflow"
    )
    workflow_type: str = Field(
        ..., 
        description="development|testing|deployment|review"
    )
    steps: List[str] = Field(
        ..., 
        description="Ordered list of workflow steps"
    )
    tools_involved: List[str] = Field(
        ..., 
        description="Tools used in workflow"
    )
    time_estimate: int = Field(
        ..., 
        description="Typical time in minutes"
    )
    success_criteria: List[str] = Field(
        ..., 
        description="How to know it worked"
    )
    common_variations: List[str] = Field(
        default_factory=list,
        description="Alternative approaches"
    )
    automation_level: float = Field(
        default=0.0,
        description="0.0-1.0 how automated"
    )
    satisfaction_rating: float = Field(
        ..., 
        description="0.0-1.0 satisfaction with workflow"
    )
    improvement_ideas: List[str] = Field(
        default_factory=list,
        description="Potential enhancements"
    )
    last_used: datetime = Field(
        default_factory=datetime.now,
        description="When workflow was last used"
    )


# Entity type mapping for MCP server integration
SUPERCLAUDE_ENTITY_TYPES = {
    "DevelopmentPreference": DevelopmentPreference,
    "ProjectExperience": ProjectExperience,
    "DecisionHistory": DecisionHistory,
    "ToolUsagePattern": ToolUsagePattern,
    "LearningProgress": LearningProgress,
    "ProblemSolution": ProblemSolution,
    "CodePattern": CodePattern,
    "WorkflowPreference": WorkflowPreference,
}


def get_entity_schema(entity_type: str) -> dict:
    """Get JSON schema for entity type"""
    if entity_type in SUPERCLAUDE_ENTITY_TYPES:
        return SUPERCLAUDE_ENTITY_TYPES[entity_type].model_json_schema()
    else:
        raise ValueError(f"Unknown entity type: {entity_type}")


def create_entity_instance(entity_type: str, data: dict):
    """Create entity instance from data"""
    if entity_type in SUPERCLAUDE_ENTITY_TYPES:
        return SUPERCLAUDE_ENTITY_TYPES[entity_type](**data)
    else:
        raise ValueError(f"Unknown entity type: {entity_type}")
