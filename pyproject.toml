[project]
name = "graphiti-core"
description = "A temporal graph building library"
version = "0.21.0pre2"
authors = [
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
]
readme = "README.md"
license = "Apache-2.0"
requires-python = ">=3.10,<4"
dependencies = [
    "pydantic>=2.11.5",
    "neo4j>=5.26.0",
    "diskcache>=5.6.3",
    "openai>=1.91.0",
    "tenacity>=9.0.0",
    "numpy>=1.0.0",
    "python-dotenv>=1.0.1",
    "posthog>=3.0.0"
]

[project.urls]
Homepage = "https://help.getzep.com/graphiti/graphiti/overview"
Repository = "https://github.com/getzep/graphiti"

[project.optional-dependencies]
anthropic = ["anthropic>=0.49.0"]
groq = ["groq>=0.2.0"]
google-genai = ["google-genai>=1.8.0"]
kuzu = ["kuzu>=0.11.2"]
falkordb = ["falkordb>=1.1.2,<2.0.0"]
voyageai = ["voyageai>=0.2.3"]
neo4j-opensearch = ["boto3>=1.39.16", "opensearch-py>=3.0.0"]
sentence-transformers = ["sentence-transformers>=3.2.1"]
neptune = ["langchain-aws>=0.2.29", "opensearch-py>=3.0.0", "boto3>=1.39.16"]
dev = [
    "pyright>=1.1.404",
    "groq>=0.2.0",
    "anthropic>=0.49.0",
    "google-genai>=1.8.0",
    "falkordb>=1.1.2,<2.0.0",
    "kuzu>=0.11.2",
    "ipykernel>=6.29.5",
    "jupyterlab>=4.2.4",
    "diskcache-stubs>=*******.20240818",
    "langgraph>=0.2.15",
    "langchain-anthropic>=0.2.4",
    "langsmith>=0.1.108",
    "langchain-openai>=0.2.6",
    "sentence-transformers>=3.2.1",
    "transformers>=4.45.2",
    "voyageai>=0.2.3",
    "pytest>=8.3.3",
    "pytest-asyncio>=0.24.0",
    "pytest-xdist>=3.6.1",
    "ruff>=0.7.1",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.pytest.ini_options]
pythonpath = ["."]

[tool.ruff]
line-length = 100

[tool.ruff.lint]
select = [
    # pycodestyle
    "E",
    # Pyflakes
    "F",
    # pyupgrade
    "UP",
    # flake8-bugbear
    "B",
    # flake8-simplify
    "SIM",
    # isort
    "I",
]
ignore = ["E501"]

[tool.ruff.format]
quote-style = "single"
indent-style = "space"
docstring-code-format = true

[tool.pyright]
include = ["graphiti_core"]
pythonVersion = "3.10"
typeCheckingMode = "basic"
