[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "SuperClaude"
version = "4.0.8"
authors = [
    {name = "NomenAK", email = "<EMAIL>"},
    {name = "<PERSON><PERSON><PERSON>wda <PERSON>", email = "<EMAIL>"}
]
description = "SuperClaude Framework Management Hub - AI-enhanced development framework for Claude Code"
readme = "README.md"
license = "MIT"
requires-python = ">=3.8"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
"Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9", 
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Software Development :: Code Generators",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Environment :: Console",
]
keywords = ["claude", "ai", "automation", "framework", "mcp", "agents", "development", "code-generation", "assistant"]
dependencies = [
    "setuptools>=45.0.0",
    "importlib-metadata>=1.0.0; python_version<'3.8'"
]

[project.urls]
Homepage = "https://github.com/SuperClaude-Org/SuperClaude_Framework"
GitHub = "https://github.com/SuperClaude-Org/SuperClaude_Framework"
"Bug Tracker" = "https://github.com/SuperClaude-Org/SuperClaude_Framework/issues"
"Mithun Gowda B" = "https://github.com/mithun50"
"NomenAK" = "https://github.com/NomenAK"

[project.scripts]
SuperClaude = "SuperClaude.__main__:main"
superclaude = "SuperClaude.__main__:main"

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "pytest-cov>=2.0",
    "black>=22.0",
    "flake8>=4.0",
    "mypy>=0.900"
]
test = [
    "pytest>=6.0", 
    "pytest-cov>=2.0"
]

[tool.setuptools]
include-package-data = true

[tool.setuptools.packages.find]
where = ["."]
include = ["SuperClaude*", "setup*"]
exclude = ["tests*", "*.tests*", "*.tests", ".git*", ".venv*", "*.egg-info*"]

[tool.setuptools.package-data]
"setup" = ["data/*.json", "data/*.yaml", "data/*.yml", "components/*.py", "**/*.py"]
"SuperClaude" = ["*.md", "*.txt", "**/*.md", "**/*.txt", "**/*.json", "**/*.yaml", "**/*.yml"]

[tool.black]
line-length = 88
target-version = ["py38", "py39", "py310", "py311", "py312"]
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short --strict-markers"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests"
]

[tool.coverage.run]
source = ["SuperClaude", "setup"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/.*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError", 
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:"
]
show_missing = true
