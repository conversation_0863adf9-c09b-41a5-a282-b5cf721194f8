# Graphiti Entity Definitions for SuperClaude

> **Extended entity system for comprehensive memory management and biomorphic intelligence**

## Core Entity Types

### 1. Development Preferences
**Captures user's development preferences and choices across different categories**

**Key Attributes:**
- **Category**: Framework, language, tool, pattern, or architecture preference
- **Preference**: Specific preference or choice (e.g., "React over Vue", "TypeScript over JavaScript")
- **Reasoning**: Why this preference exists (performance, familiarity, team standards)
- **Confidence Score**: 0.0-1.0 confidence level in this preference
- **Context**: When this preference applies (large projects, rapid prototyping, etc.)
- **Last Updated**: When preference was last confirmed or modified
- **Success Rate**: Historical success rate when this preference was applied

**Usage Examples:**
- "I prefer TypeScript for large projects because of better type safety"
- "I use Tailwind CSS for rapid prototyping due to faster development"
- "I prefer PostgreSQL over MySQL for complex data relationships"

### 2. Project Experience
**Detailed records of project experiences and outcomes**

**Key Attributes:**
- **Project Name**: Unique identifier for the project
- **Project Type**: Web app, mobile app, API, library, tool, etc.
- **Domain**: E-commerce, fintech, healthcare, education, etc.
- **Technologies**: Complete tech stack used in the project
- **Team Size**: Number of team members involved
- **Duration**: Project timeline in weeks
- **Challenges**: Major challenges faced during development
- **Solutions**: How challenges were resolved
- **Lessons Learned**: Key takeaways and insights
- **Success Metrics**: Performance indicators and outcomes
- **Satisfaction Score**: 0.0-1.0 overall project satisfaction
- **Would Repeat**: Whether the same approach would be used again

**Usage Examples:**
- Track successful patterns across similar projects
- Learn from challenges and their solutions
- Build expertise in specific domains and technologies

### 3. Decision History
**Records of significant technical decisions and their outcomes**

**Key Attributes:**
- **Decision Context**: What decision was being made
- **Options Considered**: All alternatives that were evaluated
- **Chosen Option**: Final choice that was made
- **Decision Criteria**: Factors that influenced the decision
- **Reasoning**: Detailed reasoning for the choice
- **Stakeholders**: Who was involved in the decision process
- **Outcome**: What actually happened after the decision
- **Outcome Rating**: 0.0-1.0 rating of how well it worked
- **Lessons Learned**: What was learned from the outcome
- **Would Decide Differently**: Retrospective assessment

**Usage Examples:**
- Learn from past architectural decisions
- Understand what factors lead to successful choices
- Avoid repeating poor decisions

### 4. Tool Usage Patterns
**Patterns of tool usage and their effectiveness**

**Key Attributes:**
- **Tool Name**: Name of the tool or technology
- **Tool Category**: IDE, framework, library, service, CLI tool, etc.
- **Use Cases**: Specific scenarios where this tool is used
- **Effectiveness Rating**: 0.0-1.0 effectiveness score
- **Learning Curve**: Easy, moderate, or steep learning curve
- **Common Parameters**: Frequently used settings and configurations
- **Best Practices**: Learned best practices for using the tool
- **Common Pitfalls**: Things to avoid when using the tool
- **Alternatives Considered**: Other tools that were evaluated
- **Replacement Likelihood**: 0.0-1.0 chance of switching to alternatives
- **Last Used**: When the tool was last used

**Usage Examples:**
- Track which tools work best for specific tasks
- Remember optimal configurations and settings
- Learn from tool selection decisions

### 5. Learning Progress
**Tracks skill development and knowledge acquisition**

**Key Attributes:**
- **Skill Area**: What is being learned (React, Python, DevOps, etc.)
- **Current Level**: Beginner, intermediate, advanced, or expert
- **Learning Goals**: What the user wants to achieve
- **Resources Used**: Books, courses, tutorials, documentation
- **Practice Projects**: Projects used for skill building
- **Milestones Achieved**: Progress markers that have been reached
- **Knowledge Gaps**: Areas that need improvement
- **Next Steps**: Planned learning activities
- **Confidence Level**: 0.0-1.0 confidence in the skill
- **Last Practiced**: When the skill was last used
- **Started Learning**: When learning began

**Usage Examples:**
- Track progress in learning new technologies
- Identify knowledge gaps and learning opportunities
- Plan appropriate challenges based on skill level

### 6. Problem Solutions
**Catalog of problems encountered and their solutions**

**Key Attributes:**
- **Problem Description**: What problem was encountered
- **Problem Category**: Bug, performance, design, deployment, security
- **Context**: When and where the problem occurred
- **Symptoms**: How the problem manifested
- **Root Cause**: Underlying cause of the problem
- **Solution Steps**: How the problem was solved
- **Tools Used**: Tools that helped solve the problem
- **Time to Solve**: How long it took to resolve (in minutes)
- **Solution Effectiveness**: 0.0-1.0 rating of how well it worked
- **Prevention Measures**: How to avoid the problem in the future
- **Similar Problems**: Related issues that might occur
- **Solved Date**: When the problem was resolved

**Usage Examples:**
- Build a knowledge base of solutions
- Recognize similar problems quickly
- Apply proven solutions to new instances

### 7. Code Patterns
**Reusable code patterns and architectural approaches**

**Key Attributes:**
- **Pattern Name**: Name of the pattern
- **Pattern Type**: Design, architectural, or implementation pattern
- **Use Case**: When to use this pattern
- **Technologies**: Languages and frameworks where it applies
- **Code Example**: Sample implementation
- **Benefits**: Advantages of using this pattern
- **Drawbacks**: Potential disadvantages
- **Alternatives**: Other patterns to consider
- **Complexity Level**: Simple, moderate, or complex
- **Usage Frequency**: 0.0-1.0 how often it's used
- **Success Rate**: 0.0-1.0 success rate when applied
- **Created Date**: When the pattern was first recorded

**Usage Examples:**
- Build a library of proven patterns
- Choose appropriate patterns for new projects
- Learn from successful architectural decisions

### 8. Workflow Preferences
**Preferred workflows and development processes**

**Key Attributes:**
- **Workflow Name**: Name of the workflow
- **Workflow Type**: Development, testing, deployment, review
- **Steps**: Ordered list of workflow steps
- **Tools Involved**: Tools used in the workflow
- **Time Estimate**: Typical time required (in minutes)
- **Success Criteria**: How to know the workflow succeeded
- **Common Variations**: Alternative approaches
- **Automation Level**: 0.0-1.0 how automated the workflow is
- **Satisfaction Rating**: 0.0-1.0 satisfaction with the workflow
- **Improvement Ideas**: Potential enhancements
- **Last Used**: When the workflow was last used

**Usage Examples:**
- Standardize effective development processes
- Optimize workflows based on experience
- Adapt workflows to different project contexts

## Relationship Types

### Primary Relationships
- **PREFERS**: User → DevelopmentPreference
- **EXPERIENCED_WITH**: User → ProjectExperience  
- **DECIDED**: User → DecisionHistory
- **USES**: User → ToolUsagePattern
- **LEARNING**: User → LearningProgress
- **SOLVED**: User → ProblemSolution
- **APPLIES**: User → CodePattern
- **FOLLOWS**: User → WorkflowPreference

### Secondary Relationships
- **SIMILAR_TO**: ProjectExperience → ProjectExperience
- **LEADS_TO**: DecisionHistory → ProjectExperience
- **REQUIRES**: CodePattern → ToolUsagePattern
- **IMPROVES**: LearningProgress → ToolUsagePattern
- **PREVENTS**: ProblemSolution → ProblemSolution
- **ENABLES**: WorkflowPreference → ProjectExperience
- **CONFLICTS_WITH**: DevelopmentPreference → DevelopmentPreference
- **SUPERSEDES**: ToolUsagePattern → ToolUsagePattern

### Temporal Relationships
- **BEFORE**: Any → Any (temporal ordering)
- **AFTER**: Any → Any (temporal ordering)
- **DURING**: Any → Any (concurrent events)
- **CAUSED_BY**: Any → Any (causal relationships)

## Entity Evolution Patterns

### Preference Evolution
**How user preferences develop and change over time:**
- Initial preferences based on limited experience
- Preferences tested through project experiences
- Confidence scores adjust based on success rates
- Preferences evolve as user gains expertise
- Conflicting preferences resolved through experience

### Skill Development
**Natural progression of learning and expertise:**
- Learning goals established based on interests and needs
- Practice through projects and exercises
- Milestones achieved mark progress points
- Current level advances based on demonstrated competency
- New goals emerge as skills develop

### Problem Learning
**How problem-solving knowledge accumulates:**
- Problems encountered and documented
- Solutions developed and tested
- Outcomes evaluated for effectiveness
- Patterns recognized across similar problems
- Prevention strategies developed for future use

## Memory Query Patterns

### Preference-Based Queries
- Find preferences by category and context
- Retrieve high-confidence preferences for decision making
- Identify conflicting preferences that need resolution

### Experience-Based Queries
- Find similar projects by domain and technology
- Retrieve successful patterns from past projects
- Learn from challenges and solutions in comparable contexts

### Pattern Recognition Queries
- Identify recurring successful approaches
- Detect patterns that lead to problems
- Find correlations between choices and outcomes

### Learning-Oriented Queries
- Track progress in specific skill areas
- Identify knowledge gaps and learning opportunities
- Find practice projects appropriate for current skill level

## Integration Guidelines

### Memory-First Approach
- Always query relevant memories before making recommendations
- Apply learned preferences to guide suggestions
- Use historical success patterns to inform decisions
- Learn from every interaction and outcome

### Context Awareness
- Consider project domain, technology stack, and team size
- Apply appropriate patterns based on current context
- Adapt recommendations to user's skill level and experience

### Continuous Learning
- Update confidence scores based on outcomes
- Evolve preferences as user gains experience
- Strengthen successful patterns and weaken ineffective ones
- Capture new insights and integrate with existing knowledge

This entity system provides the foundation for SuperClaude's biomorphic intelligence, enabling it to learn, adapt, and provide increasingly personalized assistance based on accumulated experience and demonstrated preferences.
