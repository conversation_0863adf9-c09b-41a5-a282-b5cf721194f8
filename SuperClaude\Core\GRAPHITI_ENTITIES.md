# Graphiti Entity Definitions for SuperClaude

> **Extended entity system for comprehensive memory management and biomorphic intelligence**

## Core Entity Types

### 1. Development Preferences
```python
class DevelopmentPreference(BaseModel):
    """User's development preferences and choices"""
    category: str = Field(..., description="framework|language|tool|pattern|architecture")
    preference: str = Field(..., description="Specific preference or choice")
    reasoning: str = Field(..., description="Why this preference exists")
    confidence_score: float = Field(..., description="0.0-1.0 confidence level")
    context: str = Field(..., description="When this preference applies")
    last_updated: datetime = Field(..., description="When preference was last confirmed")
    success_rate: float = Field(..., description="Historical success rate with this preference")
```

### 2. Project Experience
```python
class ProjectExperience(BaseModel):
    """Detailed project experience and outcomes"""
    project_name: str = Field(..., description="Project identifier")
    project_type: str = Field(..., description="web_app|mobile_app|api|library|tool")
    domain: str = Field(..., description="e-commerce|fintech|healthcare|education|etc")
    technologies: List[str] = Field(..., description="Tech stack used")
    team_size: int = Field(..., description="Number of team members")
    duration_weeks: int = Field(..., description="Project duration")
    challenges: List[str] = Field(..., description="Major challenges faced")
    solutions: List[str] = Field(..., description="How challenges were solved")
    lessons_learned: List[str] = Field(..., description="Key takeaways")
    success_metrics: Dict[str, Any] = Field(..., description="Performance indicators")
    satisfaction_score: float = Field(..., description="0.0-1.0 project satisfaction")
    would_repeat: bool = Field(..., description="Would use same approach again")
```

### 3. Decision History
```python
class DecisionHistory(BaseModel):
    """Record of significant technical decisions"""
    decision_context: str = Field(..., description="What decision was being made")
    options_considered: List[str] = Field(..., description="All options evaluated")
    chosen_option: str = Field(..., description="Final choice made")
    decision_criteria: List[str] = Field(..., description="Factors that influenced decision")
    reasoning: str = Field(..., description="Detailed reasoning for choice")
    stakeholders: List[str] = Field(..., description="Who was involved in decision")
    outcome: str = Field(..., description="What actually happened")
    outcome_rating: float = Field(..., description="0.0-1.0 how well it worked")
    lessons_learned: str = Field(..., description="What was learned from outcome")
    would_decide_differently: bool = Field(..., description="Retrospective assessment")
```

### 4. Tool Usage Pattern
```python
class ToolUsagePattern(BaseModel):
    """Patterns of tool usage and effectiveness"""
    tool_name: str = Field(..., description="Name of tool or technology")
    tool_category: str = Field(..., description="ide|framework|library|service|cli")
    use_cases: List[str] = Field(..., description="When this tool is used")
    effectiveness_rating: float = Field(..., description="0.0-1.0 effectiveness score")
    learning_curve: str = Field(..., description="easy|moderate|steep")
    common_parameters: Dict[str, Any] = Field(..., description="Frequently used settings")
    best_practices: List[str] = Field(..., description="Learned best practices")
    common_pitfalls: List[str] = Field(..., description="Things to avoid")
    alternatives_considered: List[str] = Field(..., description="Other tools evaluated")
    replacement_likelihood: float = Field(..., description="0.0-1.0 chance of switching")
```

### 5. Learning Progress
```python
class LearningProgress(BaseModel):
    """Track skill development and knowledge acquisition"""
    skill_area: str = Field(..., description="What is being learned")
    current_level: str = Field(..., description="beginner|intermediate|advanced|expert")
    learning_goals: List[str] = Field(..., description="What user wants to achieve")
    resources_used: List[str] = Field(..., description="Books, courses, tutorials")
    practice_projects: List[str] = Field(..., description="Projects for skill building")
    milestones_achieved: List[str] = Field(..., description="Progress markers reached")
    knowledge_gaps: List[str] = Field(..., description="Areas needing improvement")
    next_steps: List[str] = Field(..., description="Planned learning activities")
    confidence_level: float = Field(..., description="0.0-1.0 confidence in skill")
    last_practiced: datetime = Field(..., description="When skill was last used")
```

### 6. Problem Solution
```python
class ProblemSolution(BaseModel):
    """Catalog of problems and their solutions"""
    problem_description: str = Field(..., description="What problem was encountered")
    problem_category: str = Field(..., description="bug|performance|design|deployment")
    context: str = Field(..., description="When/where problem occurred")
    symptoms: List[str] = Field(..., description="How problem manifested")
    root_cause: str = Field(..., description="Underlying cause of problem")
    solution_steps: List[str] = Field(..., description="How problem was solved")
    tools_used: List[str] = Field(..., description="Tools that helped solve it")
    time_to_solve: int = Field(..., description="Minutes to resolve")
    solution_effectiveness: float = Field(..., description="0.0-1.0 how well it worked")
    prevention_measures: List[str] = Field(..., description="How to avoid in future")
    similar_problems: List[str] = Field(..., description="Related issues")
```

### 7. Code Pattern
```python
class CodePattern(BaseModel):
    """Reusable code patterns and architectures"""
    pattern_name: str = Field(..., description="Name of the pattern")
    pattern_type: str = Field(..., description="design|architectural|implementation")
    use_case: str = Field(..., description="When to use this pattern")
    technologies: List[str] = Field(..., description="Languages/frameworks it applies to")
    code_example: str = Field(..., description="Sample implementation")
    benefits: List[str] = Field(..., description="Advantages of using pattern")
    drawbacks: List[str] = Field(..., description="Potential disadvantages")
    alternatives: List[str] = Field(..., description="Other patterns to consider")
    complexity_level: str = Field(..., description="simple|moderate|complex")
    usage_frequency: float = Field(..., description="0.0-1.0 how often used")
    success_rate: float = Field(..., description="0.0-1.0 success when applied")
```

### 8. Workflow Preference
```python
class WorkflowPreference(BaseModel):
    """Preferred workflows and processes"""
    workflow_name: str = Field(..., description="Name of workflow")
    workflow_type: str = Field(..., description="development|testing|deployment|review")
    steps: List[str] = Field(..., description="Ordered list of workflow steps")
    tools_involved: List[str] = Field(..., description="Tools used in workflow")
    time_estimate: int = Field(..., description="Typical time in minutes")
    success_criteria: List[str] = Field(..., description="How to know it worked")
    common_variations: List[str] = Field(..., description="Alternative approaches")
    automation_level: float = Field(..., description="0.0-1.0 how automated")
    satisfaction_rating: float = Field(..., description="0.0-1.0 satisfaction with workflow")
    improvement_ideas: List[str] = Field(..., description="Potential enhancements")
```

## Relationship Types

### Primary Relationships
- **PREFERS**: User → DevelopmentPreference
- **EXPERIENCED_WITH**: User → ProjectExperience  
- **DECIDED**: User → DecisionHistory
- **USES**: User → ToolUsagePattern
- **LEARNING**: User → LearningProgress
- **SOLVED**: User → ProblemSolution
- **APPLIES**: User → CodePattern
- **FOLLOWS**: User → WorkflowPreference

### Secondary Relationships
- **SIMILAR_TO**: ProjectExperience → ProjectExperience
- **LEADS_TO**: DecisionHistory → ProjectExperience
- **REQUIRES**: CodePattern → ToolUsagePattern
- **IMPROVES**: LearningProgress → ToolUsagePattern
- **PREVENTS**: ProblemSolution → ProblemSolution
- **ENABLES**: WorkflowPreference → ProjectExperience
- **CONFLICTS_WITH**: DevelopmentPreference → DevelopmentPreference
- **SUPERSEDES**: ToolUsagePattern → ToolUsagePattern

### Temporal Relationships
- **BEFORE**: Any → Any (temporal ordering)
- **AFTER**: Any → Any (temporal ordering)
- **DURING**: Any → Any (concurrent events)
- **CAUSED_BY**: Any → Any (causal relationships)

## Entity Evolution Patterns

### Preference Evolution
```
Initial Preference → Experience → Outcome → Updated Preference
confidence_score adjusts based on success_rate
```

### Skill Development
```
Learning Goal → Practice → Project → Milestone → Next Goal
current_level advances based on milestones_achieved
```

### Problem Learning
```
Problem → Solution → Outcome → Pattern Recognition → Prevention
similar_problems links help identify patterns
```

## Query Optimization Strategies

### Common Query Patterns
1. **Preference Lookup**: Find preferences by category and context
2. **Experience Search**: Find similar projects by type and technologies
3. **Solution Retrieval**: Find solutions for similar problems
4. **Pattern Matching**: Identify applicable code patterns
5. **Tool Recommendation**: Suggest tools based on use case and effectiveness

### Index Recommendations
```cypher
CREATE INDEX FOR (n:DevelopmentPreference) ON (n.category, n.context)
CREATE INDEX FOR (n:ProjectExperience) ON (n.project_type, n.domain)
CREATE INDEX FOR (n:ProblemSolution) ON (n.problem_category, n.context)
CREATE INDEX FOR (n:ToolUsagePattern) ON (n.tool_category, n.use_cases)
CREATE INDEX FOR (n:CodePattern) ON (n.pattern_type, n.technologies)
```

## Integration with SuperClaude Commands

### Command Enhancement Mapping
- **brainstorm**: Query ProjectExperience + DevelopmentPreference
- **design**: Query CodePattern + ToolUsagePattern
- **implement**: Query ProblemSolution + WorkflowPreference
- **troubleshoot**: Query ProblemSolution + DecisionHistory
- **reflect**: Update all relevant entities based on outcomes
- **improve**: Query LearningProgress + identify improvement opportunities
