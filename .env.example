# SuperClaude Environment Variables
# Copy this file to .env and fill in your actual values

# PyPI API Tokens
PYPI_API_TOKEN=pypi-your-production-token-here
TEST_PYPI_API_TOKEN=pypi-your-test-token-here

# GitHub Secrets (for CI/CD)
# Add these to your GitHub repository settings:
# Settings → Secrets and variables → Actions
# PYPI_API_TOKEN=pypi-your-production-token-here
# TEST_PYPI_API_TOKEN=pypi-your-test-token-here