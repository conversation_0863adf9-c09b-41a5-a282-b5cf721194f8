---
name: memory-decide
description: "Memory-driven decision support system with intelligent recommendations"
category: intelligence
complexity: advanced
mcp-servers: [graphiti, sequential, context7]
personas: [architect, analyzer]
---

# /sc:memory-decide - Memory-Driven Decision Support

> **Context Framework Note**: This provides behavioral instructions for memory-enhanced decision making across all SuperClaude operations.

## Triggers
- Architecture decisions requiring historical context
- Tool selection with multiple viable options
- Technology stack choices for new projects
- Problem-solving approaches with known patterns
- Workflow optimization opportunities
- Risk assessment based on past experiences

## Context Trigger Pattern
```
/sc:memory-decide [decision-context] [--analyze-history] [--risk-assessment] [--recommend]
```
**Usage**: Activate memory-driven decision support for complex choices with historical precedent.

## Decision Support Framework

### 1. Memory Query Phase
```
Query Sequence:
1. Similar past decisions in same context
2. Outcomes and satisfaction ratings
3. User preferences and confidence levels
4. Related experiences and lessons learned
5. Tool effectiveness patterns
6. Risk factors from historical data
```

### 2. Analysis Phase
```
Analysis Framework:
1. Pattern Recognition: Identify recurring decision patterns
2. Success Correlation: Link decisions to positive outcomes
3. Risk Assessment: Evaluate potential negative outcomes
4. Context Matching: Find most relevant historical precedents
5. Preference Alignment: Match with current user preferences
6. Confidence Scoring: Rate recommendation confidence
```

### 3. Recommendation Generation
```
Recommendation Process:
1. Rank options by historical success rate
2. Weight by user preference strength
3. Adjust for context similarity
4. Factor in risk tolerance
5. Consider learning opportunities
6. Generate confidence-scored recommendations
```

## Memory-Enhanced Decision Types

### Architecture Decisions
```python
def architecture_decision(context, requirements):
    # Query past architectural choices
    past_architectures = query_memory(
        entity_type="DecisionHistory",
        context_filter="architecture",
        requirements_match=requirements
    )
    
    # Analyze success patterns
    success_patterns = analyze_outcomes(past_architectures)
    
    # Generate recommendations
    return recommend_architecture(
        patterns=success_patterns,
        preferences=get_user_preferences("architecture"),
        risk_tolerance=get_risk_profile()
    )
```

### Tool Selection
```python
def tool_selection(use_case, constraints):
    # Query tool usage patterns
    tool_patterns = query_memory(
        entity_type="ToolUsagePattern",
        use_case_match=use_case,
        effectiveness_threshold=0.7
    )
    
    # Consider user preferences
    tool_preferences = get_preferences("tool", use_case)
    
    # Generate ranked recommendations
    return rank_tools(
        patterns=tool_patterns,
        preferences=tool_preferences,
        constraints=constraints
    )
```

### Technology Stack Decisions
```python
def technology_stack(project_type, requirements):
    # Find similar successful projects
    similar_projects = query_memory(
        entity_type="ProjectExperience",
        project_type=project_type,
        satisfaction_threshold=0.8
    )
    
    # Extract successful tech combinations
    successful_stacks = extract_tech_stacks(similar_projects)
    
    # Match with current preferences
    return recommend_stack(
        successful_stacks=successful_stacks,
        current_preferences=get_tech_preferences(),
        requirements=requirements
    )
```

## Decision Recording System

### Decision Capture
```python
class DecisionCapture:
    def record_decision(self, context, options, choice, reasoning):
        decision = DecisionHistory(
            decision_context=context,
            options_considered=options,
            chosen_option=choice,
            reasoning=reasoning,
            timestamp=datetime.now()
        )
        
        # Store in Graphiti
        graphiti.add_entity(decision)
        
        # Create relationships
        self.link_to_preferences(decision)
        self.link_to_experiences(decision)
```

### Outcome Tracking
```python
class OutcomeTracker:
    def track_outcome(self, decision_id, outcome, rating):
        # Update decision with outcome
        decision = graphiti.get_entity(decision_id)
        decision.outcome = outcome
        decision.outcome_rating = rating
        
        # Update related preferences
        self.update_preference_confidence(decision)
        
        # Learn from outcome
        self.extract_lessons(decision)
```

## Intelligent Recommendation Engine

### Preference Learning
```python
class PreferenceLearner:
    def update_preferences(self, decision, outcome_rating):
        if outcome_rating > 0.8:
            # Strengthen preferences that led to good outcomes
            self.strengthen_preferences(decision.chosen_option)
        elif outcome_rating < 0.4:
            # Weaken preferences that led to poor outcomes
            self.weaken_preferences(decision.chosen_option)
        
        # Update confidence scores
        self.adjust_confidence(decision.reasoning)
```

### Risk Assessment
```python
class RiskAssessor:
    def assess_risk(self, proposed_decision):
        # Find similar past decisions
        similar_decisions = self.find_similar(proposed_decision)
        
        # Calculate risk metrics
        failure_rate = self.calculate_failure_rate(similar_decisions)
        impact_severity = self.assess_impact(proposed_decision)
        
        # Generate risk profile
        return RiskProfile(
            probability=failure_rate,
            impact=impact_severity,
            mitigation_strategies=self.suggest_mitigations()
        )
```

## Integration with SuperClaude Commands

### Enhanced Brainstorming
```
/sc:brainstorm "mobile app architecture"
→ Query past mobile projects
→ Apply architectural preferences
→ Suggest proven patterns
→ Warn about past pitfalls
```

### Intelligent Implementation
```
/sc:implement "user authentication"
→ Query auth implementation history
→ Recommend proven libraries
→ Apply security preferences
→ Suggest testing strategies
```

### Smart Tool Selection
```
/sc:select-tool "database optimization"
→ Query optimization experiences
→ Rank tools by effectiveness
→ Consider current tech stack
→ Recommend best approach
```

## Biomorphic Decision Features

### Adaptive Learning
- **Pattern Recognition**: Identify successful decision patterns
- **Preference Evolution**: Adjust preferences based on outcomes
- **Context Sensitivity**: Apply different strategies for different scenarios

### Wisdom Development
- **Experience Integration**: Combine related decisions for insights
- **Mistake Learning**: Learn from poor outcomes to avoid repetition
- **Success Amplification**: Identify and replicate successful patterns

### Personalized Intelligence
- **Individual Adaptation**: Learn unique decision-making style
- **Risk Tolerance**: Adapt to personal risk preferences
- **Growth Tracking**: Monitor decision-making improvement over time

## Performance Metrics

### Decision Quality Metrics
- **Outcome Satisfaction**: Average rating of decision outcomes
- **Preference Alignment**: How well decisions match stated preferences
- **Risk Accuracy**: How well risk assessments predict actual outcomes
- **Learning Rate**: How quickly decision quality improves

### System Performance
- **Query Speed**: <200ms for preference lookup
- **Analysis Time**: <1s for complex decision analysis
- **Recommendation Accuracy**: >80% user satisfaction with recommendations
- **Memory Utilization**: Efficient use of historical data

## Examples

### Architecture Decision
```
Context: "Choose frontend framework for e-commerce site"
Memory Query: Past e-commerce projects, React vs Vue experiences
Analysis: React projects had 0.9 satisfaction, Vue had 0.7
Recommendation: "React (confidence: 0.85) based on your past e-commerce success"
```

### Tool Selection
```
Context: "Database for high-traffic application"
Memory Query: Performance requirements, past database choices
Analysis: PostgreSQL performed well in similar contexts
Recommendation: "PostgreSQL with Redis caching (confidence: 0.92)"
```

### Risk Assessment
```
Context: "Microservices architecture for new project"
Memory Query: Past microservices experiences
Analysis: 60% success rate, complexity challenges noted
Recommendation: "Consider monolith first (risk: medium, complexity: high)"
```
