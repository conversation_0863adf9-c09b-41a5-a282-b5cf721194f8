# Graphiti MCP Server

**Purpose**: Temporal knowledge graph memory system for persistent learning and context-aware decision making

## Triggers
- Session initialization: `/sc:load`, project activation, memory-driven workflows
- Decision support: tool selection, architecture choices, problem-solving patterns
- Learning capture: experience recording, preference updates, pattern recognition
- Context retrieval: historical project analysis, similar problem identification
- Brainstorming enhancement: prompt optimization, requirement clarification
- Cross-session persistence: knowledge continuity, progressive learning

## Choose When
- **Over Serena**: For long-term memory vs session persistence
- **For knowledge graphs**: Entity relationships, temporal awareness, learning patterns
- **For decision support**: Historical experience, preference-based recommendations
- **For context enhancement**: Rich background information, pattern recognition
- **Not for simple storage**: Basic session data, temporary variables

## Works Best With
- **Sequential**: Graphiti provides context → Sequential performs complex reasoning
- **Serena**: Graphiti handles long-term memory → Serena manages session state
- **Context7**: Graphiti enriches with experience → Context7 provides current docs
- **All Commands**: Memory-first approach enhances every operation

## Memory Architecture

### Core Entity Types
```python
# Development Preferences
class DevelopmentPreference(BaseModel):
    category: str  # "framework", "language", "tool", "pattern"
    preference: str
    reasoning: str
    confidence_score: float
    last_updated: datetime

# Project Experience  
class ProjectExperience(BaseModel):
    project_type: str
    technologies: List[str]
    challenges: List[str]
    solutions: List[str]
    lessons_learned: List[str]
    success_metrics: Dict[str, Any]

# Decision History
class DecisionHistory(BaseModel):
    context: str
    options_considered: List[str]
    chosen_option: str
    reasoning: str
    outcome: str
    satisfaction_score: float

# Tool Usage Pattern
class ToolUsagePattern(BaseModel):
    tool_name: str
    use_cases: List[str]
    effectiveness_rating: float
    common_parameters: Dict[str, Any]
    best_practices: List[str]
```

### Relationship Types
- **PREFERS**: User → Technology/Pattern/Tool
- **EXPERIENCED_WITH**: User → Project/Technology
- **LEARNED_FROM**: User → Experience/Mistake
- **SIMILAR_TO**: Project → Project
- **SOLVED_BY**: Problem → Solution
- **LEADS_TO**: Decision → Outcome

## Integration Points

### 1. Memory-First Workflow
```
Every operation starts with:
1. Query relevant memories
2. Apply learned preferences
3. Consider historical patterns
4. Execute with context
5. Record new experience
```

### 2. Brainstorm Mode Enhancement
- Query similar past projects
- Retrieve relevant preferences
- Apply learned patterns
- Suggest based on experience
- Record new insights

### 3. Decision Support System
- Historical decision analysis
- Preference-based recommendations
- Pattern recognition
- Outcome prediction
- Learning from mistakes

## Examples

### Memory-Enhanced Brainstorming
```
User: "I want to build a web app"
Graphiti Query: Past web projects, preferred frameworks, successful patterns
Enhanced Response: "Based on your previous React+TypeScript projects and 
preference for Tailwind CSS, I recommend..."
```

### Context-Aware Tool Selection
```
Task: "Optimize database queries"
Graphiti Query: Past optimization experiences, tool effectiveness
Smart Selection: PostgreSQL EXPLAIN + pgAdmin (based on past success)
```

### Learning from Experience
```
After project completion:
- Record what worked well
- Document challenges faced
- Update tool preferences
- Strengthen successful patterns
- Learn from any mistakes
```

## Biomorphic Features

### Adaptive Learning
- Preference evolution based on outcomes
- Pattern recognition across projects
- Continuous improvement of recommendations

### Contextual Memory
- Time-aware knowledge retrieval
- Project-specific experience application
- Cross-domain pattern transfer

### Personalized Decision Making
- Individual preference weighting
- Historical success factor analysis
- Risk assessment based on past outcomes

## Performance Optimization
- **Memory Queries**: <100ms for preference lookup
- **Context Retrieval**: <500ms for complex pattern matching
- **Learning Updates**: Asynchronous background processing
- **Graph Traversal**: Optimized for common query patterns
