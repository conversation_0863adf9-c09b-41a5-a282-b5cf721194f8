# Graphiti MCP Server

**Purpose**: Temporal knowledge graph memory system for persistent learning and context-aware decision making

## Triggers
- Session initialization: `/sc:load`, project activation, memory-driven workflows
- Decision support: tool selection, architecture choices, problem-solving patterns
- Learning capture: experience recording, preference updates, pattern recognition
- Context retrieval: historical project analysis, similar problem identification
- Brainstorming enhancement: prompt optimization, requirement clarification
- Cross-session persistence: knowledge continuity, progressive learning

## Choose When
- **Over Serena**: For long-term memory vs session persistence
- **For knowledge graphs**: Entity relationships, temporal awareness, learning patterns
- **For decision support**: Historical experience, preference-based recommendations
- **For context enhancement**: Rich background information, pattern recognition
- **Not for simple storage**: Basic session data, temporary variables

## Works Best With
- **Sequential**: Graphiti provides context → Se<PERSON> performs complex reasoning
- **Serena**: Graphiti handles long-term memory → Serena manages session state
- **Context7**: Graphiti enriches with experience → Context7 provides current docs
- **All Commands**: Memory-first approach enhances every operation

## Memory Architecture

### Core Entity Types

**Development Preferences**
- **Category**: Framework, language, tool, or pattern preferences
- **Preference**: Specific choice or approach preferred
- **Reasoning**: Why this preference exists
- **Confidence Score**: 0.0-1.0 confidence level in preference
- **Last Updated**: When preference was last confirmed

**Project Experience**
- **Project Type**: Web app, mobile app, API, library, etc.
- **Technologies**: Tech stack and tools used
- **Challenges**: Major obstacles encountered
- **Solutions**: How challenges were resolved
- **Lessons Learned**: Key insights and takeaways
- **Success Metrics**: Measurable outcomes and results

**Decision History**
- **Context**: What decision was being made
- **Options Considered**: All alternatives evaluated
- **Chosen Option**: Final decision made
- **Reasoning**: Logic behind the choice
- **Outcome**: What actually happened
- **Satisfaction Score**: 0.0-1.0 rating of decision quality

**Tool Usage Patterns**
- **Tool Name**: Name of tool or technology
- **Use Cases**: Scenarios where tool is effective
- **Effectiveness Rating**: 0.0-1.0 success rate
- **Common Parameters**: Frequently used configurations
- **Best Practices**: Learned optimal usage patterns

### Relationship Types
- **PREFERS**: User → Technology/Pattern/Tool
- **EXPERIENCED_WITH**: User → Project/Technology
- **LEARNED_FROM**: User → Experience/Mistake
- **SIMILAR_TO**: Project → Project
- **SOLVED_BY**: Problem → Solution
- **LEADS_TO**: Decision → Outcome

## Integration Points

### 1. Memory-First Workflow

**Every operation follows this pattern:**
1. **Query Relevant Memories**: Retrieve related experiences and preferences
2. **Apply Learned Preferences**: Use established user preferences to guide decisions
3. **Consider Historical Patterns**: Reference successful approaches from past projects
4. **Execute with Context**: Perform actions informed by historical knowledge
5. **Record New Experience**: Capture outcomes and insights for future learning

### 2. Brainstorm Mode Enhancement

**Memory-enhanced brainstorming process:**
- **Query Similar Projects**: Find comparable past projects and their approaches
- **Retrieve Preferences**: Apply user's demonstrated technology and pattern preferences
- **Apply Learned Patterns**: Use successful patterns from similar contexts
- **Suggest Based on Experience**: Provide recommendations grounded in historical success
- **Record New Insights**: Capture brainstorming outcomes for future reference

### 3. Decision Support System

**Intelligent decision assistance:**
- **Historical Analysis**: Review past decisions and their outcomes
- **Preference-Based Recommendations**: Suggest options aligned with user preferences
- **Pattern Recognition**: Identify successful approaches from similar situations
- **Outcome Prediction**: Estimate likely results based on historical data
- **Learning from Experience**: Incorporate lessons from past successes and failures

## Usage Examples

### Memory-Enhanced Brainstorming

**Scenario**: User requests help building a web app

**Memory Query Process**:
- Retrieve past web projects and their outcomes
- Identify preferred frameworks and technologies
- Reference successful patterns and architectures
- Consider lessons learned from previous challenges

**Enhanced Response**:
"Based on your previous React+TypeScript projects and demonstrated preference for Tailwind CSS, I recommend starting with a Next.js setup. Your past experience shows this combination reduced development time by 30% and resulted in high satisfaction scores."

### Context-Aware Tool Selection

**Scenario**: Database query optimization task

**Memory-Informed Process**:
- Query past optimization experiences and their effectiveness
- Identify tools that have proven successful in similar contexts
- Consider user's familiarity and preference patterns
- Reference successful optimization strategies

**Smart Recommendation**:
PostgreSQL EXPLAIN with pgAdmin interface, based on 85% success rate in past optimization tasks and user's demonstrated proficiency with these tools.

### Continuous Learning Integration

**Post-Project Learning Capture**:
- **Success Documentation**: Record what approaches worked well and why
- **Challenge Analysis**: Document obstacles encountered and resolution strategies
- **Preference Updates**: Adjust tool and pattern preferences based on outcomes
- **Pattern Strengthening**: Reinforce successful approaches for future recommendations
- **Experience Integration**: Link new insights to existing knowledge base

## Biomorphic Features

### Adaptive Learning
- Preference evolution based on outcomes
- Pattern recognition across projects
- Continuous improvement of recommendations

### Contextual Memory
- Time-aware knowledge retrieval
- Project-specific experience application
- Cross-domain pattern transfer

### Personalized Decision Making
- Individual preference weighting
- Historical success factor analysis
- Risk assessment based on past outcomes

## Performance Characteristics

### Query Performance
- **Memory Queries**: Sub-100ms response time for preference lookups
- **Context Retrieval**: Under 500ms for complex pattern matching
- **Learning Updates**: Asynchronous background processing for minimal impact
- **Graph Traversal**: Optimized indexing for common query patterns

### Scalability Features
- **Incremental Learning**: Updates existing knowledge without full rebuilds
- **Efficient Storage**: Temporal compression for historical data
- **Smart Caching**: Frequently accessed patterns cached for quick retrieval
- **Batch Processing**: Grouped operations for improved throughput
