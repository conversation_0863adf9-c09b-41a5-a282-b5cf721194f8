name: Claude <PERSON> Auto Review

on:
  pull_request:
    types: [opened, synchronize]

jobs:
  auto-review:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write
      id-token: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Automatic PR Review
        uses: anthropics/claude-code-action@v1
        with:
          anthropic_api_key: ${{ secrets.ANTHROPIC_API_KEY }}
          use_sticky_comment: true
          allowed_bots: "dependabot"
          prompt: |
            REPO: ${{ github.repository }}
            PR NUMBER: ${{ github.event.pull_request.number }}

            Please review this pull request.

            Note: The PR branch is already checked out in the current working directory.

            Focus on:
            - Code quality and best practices
            - Potential bugs or issues
            - Performance considerations
            - Security implications
            - Test coverage
            - Documentation updates if needed
            - Verify that README.md and docs are updated for any new features or config changes

            Provide constructive feedback with specific suggestions for improvement.
            Use `gh pr comment:*` for top-level comments.
            Use `mcp__github_inline_comment__create_inline_comment` to highlight specific areas of concern.
            Only your GitHub comments that you post will be seen, so don't submit your review as a normal message, just as comments.
            If the PR has already been reviewed, or there are no noteworthy changes, don't post anything.

          claude_args: |
            --allowedTools "mcp__github_inline_comment__create_inline_comment,Bash(gh pr comment:*), Bash(gh pr diff:*), Bash(gh pr view:*)"
