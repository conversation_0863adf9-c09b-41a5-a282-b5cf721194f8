# Graphiti Memory System

> **Transform SuperClaude into a learning, adaptive AI companion with persistent memory**

## Overview

The Graphiti Memory System integrates temporal knowledge graphs into SuperClaude, enabling:

- **Persistent Learning**: Remember preferences, patterns, and experiences across sessions
- **Context-Aware Decisions**: Make smarter choices based on historical data
- **Adaptive Behavior**: Evolve recommendations based on outcomes
- **Biomorphic Intelligence**: Mimic human-like learning and memory patterns

## Quick Start

### 1. Setup Graphiti MCP Server

```bash
# Install Neo4j database
# Download from https://neo4j.com/download/
# Create instance: graphiti-db
# User: neo4j, Password: graphiti123!

# Clone and setup Graphiti
git clone https://github.com/getzep/graphiti.git
cd graphiti/mcp_server
uv sync
cp .env.example .env

# Configure environment variables
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=graphiti123!
OPENAI_API_KEY=your_openai_key
MODEL_NAME=gpt-4o-mini
```

### 2. Add to SuperClaude

The Graphiti MCP server is pre-configured in SuperClaude. Simply ensure Neo4j is running and your environment variables are set.

## Core Concepts

### Memory Types

**Development Preferences**
- Framework choices (React, Vue, Angular)
- Language preferences (TypeScript, Python, Rust)
- Tool selections (VS Code extensions, CLI tools)
- Coding patterns and architectures

**Project Experiences**
- Successful project patterns
- Common challenges and solutions
- Technology stack combinations
- Performance optimization strategies

**Decision History**
- Architecture choices and outcomes
- Tool selection reasoning
- Problem-solving approaches
- Lessons learned from mistakes

**Learning Patterns**
- Skill development progression
- Knowledge gap identification
- Improvement areas
- Success metrics

### Entity Relationships

```
User --PREFERS--> Technology
User --EXPERIENCED_WITH--> Project
Project --USES--> Technology
Problem --SOLVED_BY--> Solution
Decision --LEADS_TO--> Outcome
Experience --TEACHES--> Lesson
```

## Usage Patterns

### 1. Memory-Enhanced Brainstorming

```
/sc:brainstorm "mobile app for task management"

# Graphiti automatically:
1. Queries past mobile projects
2. Retrieves UI/UX preferences  
3. Recalls successful patterns
4. Suggests based on experience
5. Records new insights
```

### 2. Context-Aware Development

```
/sc:implement "user authentication system"

# Memory-driven approach:
1. Recalls past auth implementations
2. Applies security preferences
3. Suggests proven libraries
4. Warns about past pitfalls
5. Updates experience database
```

### 3. Adaptive Tool Selection

```
/sc:select-tool "database optimization"

# Intelligent selection:
1. Analyzes past optimization tasks
2. Considers tool effectiveness ratings
3. Matches to current context
4. Recommends best approach
5. Learns from results
```

## Memory Management

### Recording Experiences

```
# Automatic recording during:
- Project completion
- Problem resolution  
- Tool usage
- Decision making
- Learning moments

# Manual recording:
/sc:reflect --type experience --record
```

### Querying Memory

```
# Find similar projects
"Show me projects similar to e-commerce platforms"

# Retrieve preferences
"What are my preferred React state management solutions?"

# Learn from history
"What challenges did I face with microservices?"
```

### Updating Preferences

```
# Preference evolution based on:
- Successful outcomes
- Efficiency improvements
- Satisfaction ratings
- Performance metrics
- User feedback
```

## Biomorphic Features

### Adaptive Learning
- **Pattern Recognition**: Identifies recurring themes and solutions
- **Preference Evolution**: Updates choices based on success rates
- **Context Sensitivity**: Applies different strategies for different scenarios

### Memory Consolidation
- **Experience Integration**: Combines related memories for deeper insights
- **Knowledge Synthesis**: Creates new understanding from past experiences
- **Wisdom Development**: Builds judgment through accumulated experience

### Personalized Intelligence
- **Individual Adaptation**: Learns your unique working style
- **Contextual Recommendations**: Suggests based on your specific history
- **Predictive Insights**: Anticipates needs based on patterns

## Advanced Features

### Cross-Session Learning
```
Session 1: Struggle with React performance
Session 2: Discover React.memo solution
Session 3: Automatically suggest React.memo for similar issues
```

### Pattern Transfer
```
Learn: Database indexing improves query performance
Apply: Suggest indexing strategies for new database projects
Evolve: Refine indexing recommendations based on outcomes
```

### Mistake Prevention
```
Remember: Past deployment issues with environment variables
Warn: Flag potential env var problems in new deployments
Improve: Suggest better configuration management approaches
```

## Best Practices

### 1. Regular Reflection
- Use `/sc:reflect` after completing tasks
- Record both successes and failures
- Update preferences based on outcomes

### 2. Detailed Context
- Provide rich context when recording experiences
- Include reasoning behind decisions
- Note environmental factors

### 3. Continuous Learning
- Review and update preferences regularly
- Experiment with new approaches
- Learn from community best practices

### 4. Memory Hygiene
- Periodically review and clean outdated preferences
- Update confidence scores based on recent experiences
- Archive obsolete technologies and patterns

## Troubleshooting

### Common Issues

**Memory Not Persisting**
- Check Neo4j connection
- Verify environment variables
- Ensure Graphiti MCP server is running

**Slow Memory Queries**
- Optimize Neo4j indexes
- Reduce query complexity
- Check database performance

**Inconsistent Recommendations**
- Review preference confidence scores
- Update outdated experiences
- Balance historical vs recent data

### Performance Tuning

```bash
# Neo4j optimization
CREATE INDEX FOR (n:DevelopmentPreference) ON (n.category)
CREATE INDEX FOR (n:ProjectExperience) ON (n.project_type)
CREATE INDEX FOR (n:DecisionHistory) ON (n.context)
```

## Integration Examples

### With Other MCP Servers

**Graphiti + Sequential**
```
Graphiti provides context → Sequential performs complex analysis
Memory-informed reasoning for better decisions
```

**Graphiti + Context7**
```
Graphiti enriches with experience → Context7 provides current docs
Combines historical knowledge with latest information
```

**Graphiti + Magic**
```
Graphiti recalls UI preferences → Magic generates components
Personalized UI generation based on past choices
```

## Future Enhancements

- **Team Memory**: Shared knowledge graphs for team collaboration
- **Project Templates**: Auto-generate project structures from successful patterns
- **Skill Tracking**: Monitor and guide professional development
- **Predictive Analytics**: Forecast project success based on historical data
